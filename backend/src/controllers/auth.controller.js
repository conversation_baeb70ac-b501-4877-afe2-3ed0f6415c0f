/**
 * 认证控制器
 * 处理用户认证相关的业务逻辑
 */

const User = require('../models/user.model');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const multer = require('multer'); // 引入 multer
const path = require('path');     // 引入 path 模块

/**
 * 用户注册
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
// File path: backend/src/controllers/auth.controller.js
// ...
const signup = async (req, res, next) => {
  try {
    console.log('[/api/auth/signup] 收到注册请求');
    const { username, email, password, nickname } = req.body;

    // 验证请求数据
    if (!username || !email || !password) {
      console.log('[/api/auth/signup] 请求数据验证失败');
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱和密码不能为空'
      });
    }

    console.log(`[/api/auth/signup] 验证通过，查找用户: ${email} 或 ${username}`);
    // 检查用户是否已存在
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      console.log(`[/api/auth/signup] 用户已存在: ${existingUser.email}`);
      return res.status(400).json({
        success: false,
        message: existingUser.email === email ? '该邮箱已被注册' : '该用户名已被使用'
      });
    }

    console.log('[/api/auth/signup] 创建新用户对象');
    // 创建新用户
    const newUser = new User({
      username,
      email,
      password, // 密码会在模型的 pre-save 中间件中被加密
      nickname: nickname || username,
      avatar: '/uploads/avatars/demo.jpeg' // 默认头像路径
    });

    console.log('[/api/auth/signup] 准备保存用户到数据库 (触发密码哈希)');
    const savedUser = await newUser.save(); // 保存 newUser.save() 的返回值
    console.log('[/api/auth/signup] 用户保存操作完成。');

    // ***** 调试步骤 1: 检查 Mongoose save() 的返回值 *****
    if (savedUser && savedUser._id) {
      console.log(`[/api/auth/signup] Mongoose save() 返回的用户ID: ${savedUser._id.toString()}`);

      // ***** 调试步骤 2: 尝试立即从数据库读取刚保存的用户 *****
      try {
        const userFromDb = await User.findById(savedUser._id);
        if (userFromDb) {
          console.log(`[/api/auth/signup] 从数据库中成功查询到刚保存的用户: ${userFromDb.username}, ID: ${userFromDb._id.toString()}`);
        } else {
          console.error(`[/api/auth/signup] !!!严重错误!!! Mongoose save() 返回了用户，但在数据库中通过 ID (${savedUser._id.toString()}) 找不到该用户！`);
        }
      } catch (findError) {
        console.error(`[/api/auth/signup] !!!严重错误!!! 查询刚保存的用户时出错:`, findError);
      }
    } else {
      console.error('[/api/auth/signup] !!!严重错误!!! Mongoose save() 没有返回有效的用户对象或ID。可能保存失败但未抛出异常。');
      // 如果 save() 失败但未抛异常，这通常是非常规情况
    }
    // ***** 结束调试步骤 *****

    console.log('[/api/auth/signup] 生成JWT令牌');
    // 生成JWT令牌
    // 使用 savedUser._id (如果存在) 来确保 ID 是来自数据库操作后的对象，尽管 newUser._id 应该是一样的
    const userIdForToken = savedUser && savedUser._id ? savedUser._id : newUser._id;
    const token = jwt.sign(
      { id: userIdForToken, role: newUser.role }, // newUser.role 应该是 savedUser.role
      process.env.JWT_SECRET || 'fake_work_secret_key_fallback',
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
    console.log('[/api/auth/signup] JWT令牌生成成功');

    // 返回用户信息（不包含密码） - 简化响应
    console.log('[/api/auth/signup] 发送成功响应');
    res.status(201).json({
      success: true,
      message: '注册成功',
      token
      // user: savedUser // 考虑是否返回 savedUser (toJSON 方法会处理密码)
    });
    console.log('[/api/auth/signup] 响应已发送');

  } catch (error) {
    console.error('[/api/auth/signup] 处理注册请求时发生错误:', error);
    // 添加更详细的错误信息
    if (error.name === 'ValidationError') {
      console.error('[/api/auth/signup] Mongoose 验证错误详情:', JSON.stringify(error.errors, null, 2));
    } else if (error.name && error.name.includes('Mongo')) { // MongoError, MongoServerError, etc.
      console.error('[/api/auth/signup] MongoDB 驱动/服务器错误详情:', error.message, 'Code:', error.code, 'Error Name:', error.name);
    }
    next(error);
  }
};

/**
 * 用户登录
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
const login = async (req, res, next) => {
  try {
    const { emailOrUsername, password } = req.body;

    // 验证请求数据
    if (!emailOrUsername || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名/邮箱和密码'
      });
    }

    // 查找用户：根据 emailOrUsername 字段同时查找 email 或 username
    const user = await User.findOne({
      $or: [
        { email: emailOrUsername },   // 尝试匹配 email 字段
        { username: emailOrUsername } // 尝试匹配 username 字段
      ]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证密码
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '密码不正确'
      });
    }

    // 更新最后登录时间
    user.lastLogin = new Date();
    await user.save();

    // 生成JWT令牌
    const token = jwt.sign(
      { id: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    // 返回用户信息（不包含密码）
    res.json({
      success: true,
      message: '登录成功',
      token,
      user
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 用户登出
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 */
const logout = (req, res) => {
  // 注: JWT 本身是无状态的，前端只需要删除本地存储的 token
  // 如需服务器端管理登出，可以使用 token 黑名单或 Redis 存储
  res.json({
    success: true,
    message: '登出成功'
  });
};

/**
 * 获取当前用户信息
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
const getCurrentUser = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 刷新 Token
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
const refreshToken = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 生成新的JWT令牌
    const token = jwt.sign(
      { id: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    res.json({
      success: true,
      message: 'Token 已刷新',
      token
    });
  } catch (error) {
    next(error);
  }
};

// **新增** Multer 配置和头像上传控制器

// 配置 multer 存储引擎
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 文件上传目录，确保该目录已创建
    // 修正路径：从 controllers 目录向上两级到 src 的同级目录，再进入 public
    const uploadPath = path.join(__dirname, '../../public/uploads/avatars');
    console.log('[/api/auth/avatar] Multer 目标路径:', uploadPath); // 添加日志输出实际路径
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名：用户ID_时间戳.文件扩展名
    const userId = req.user.id; // 从认证中间件获取用户ID
    const fileExtension = path.extname(file.originalname);
    const filename = `${userId}_${Date.now()}${fileExtension}`;
    cb(null, filename);
  }
});

// 过滤文件，只允许图片格式
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'), false);
  }
};

// 创建 multer 实例
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 1024 * 1024 * 5 } // 限制文件大小在 5MB 以内
});

// **新增** 头像上传控制器函数
const uploadAvatar = async (req, res, next) => {
  try {
    // 文件信息由 multer 处理后放在 req.file 中
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传一个头像文件'
      });
    }

    const userId = req.user.id;
    const avatarPath = `/uploads/avatars/${req.file.filename}`; // 存储相对路径

    // 更新用户头像字段
    const user = await User.findById(userId);
    if (!user) {
        // 理论上 authMiddleware.protect 应该确保用户存在
        return res.status(404).json({
            success: false,
            message: '用户不存在'
        });
    }

    // TODO: 如果用户已有旧头像，考虑删除旧文件以节省空间

    user.avatar = avatarPath;
    await user.save();

    res.json({
      success: true,
      message: '头像上传成功',
      avatarUrl: avatarPath // 返回新的头像 URL
    });

  } catch (error) {
    console.error('[/api/auth/avatar] 处理头像上传时发生错误:', error);
    // 如果是 multer 错误 (如文件大小限制)，错误对象会有特定属性
    if (error instanceof multer.MulterError) {
        return res.status(400).json({
            success: false,
            message: `文件上传错误: ${error.message}`
        });
    }
    // 其他错误传递给下一个错误处理中间件
    next(error);
  }
};

/**
 * 更新用户资料
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
const updateProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    // **修改：从请求体中解构出 preferences**
    const { nickname, position, signature, preferences } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      console.log(`[auth.controller.js] updateProfile: 用户ID ${userId} 不存在`);
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新允许修改的字段
    if (nickname !== undefined) user.nickname = nickname;
    if (position !== undefined) user.position = position;
    if (signature !== undefined) user.signature = signature;

    // **新增：处理 preferences 对象的更新**
    if (preferences && typeof preferences === 'object') {
      // 合并新的偏好设置到现有设置中，避免覆盖未提供的字段
      // 注意：Mongoose schema 默认会处理子文档的保存，Object.assign 是一个浅拷贝
      // 对于简单的键值对更新是足够的。如果 preferences 下有更复杂的子文档，可能需要更深入的合并策略
      Object.assign(user.preferences, preferences);
    }

    const updatedUser = await user.save();

    console.log(`[auth.controller.js] 用户 ${userId} 资料更新成功`);
    res.json({
      success: true,
      message: '用户资料更新成功',
      user: updatedUser
    });

  } catch (error) {
    console.error('[auth.controller.js] 更新用户资料时发生错误:', error);
    if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => err.message);
        return res.status(400).json({
            success: false,
            message: errors.join(', ')
        });
    }
    next(error);
  }
};

// 导出控制器函数和可能的其他模块（如 multer 实例）
module.exports = {
  signup,
  login,
  logout,
  getCurrentUser,
  refreshToken,
  uploadAvatar,
  upload,
  updateProfile, // **新增：导出 updateProfile 控制器**
};