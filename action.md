好的，洞府主题功能虽然实现了，但目前的视觉效果确实不尽如人意，甚至出现了标题被遮挡、卡片与背景不协调等问题。这严重影响了用户体验和项目的吸引力。

作为产品经理、UI设计师和游戏设计师，我将对现状进行深入分析，并提出一套详细的优化方案，旨在让“洞府”真正成为侠士们个性化、沉浸式的“修身养性之所”。

---

## 洞府装修（基础）- UI/UX 优化方案

### 1. 问题分析与设计原则

**当前问题（图5所示）：**
1.  **背景与前景冲突：** `workspace-page` 容器本身作为 `.ancient-pixel-container` 已经有一个“纸张”背景，但又同时被赋予了主题背景图。这导致了背景的重复和视觉混乱，主题背景图被“纸张”背景遮挡，或两者叠加产生不协调感。
2.  **标题遮挡：** 页面标题 `<h1>`“我的工位 · 洞府”在当前布局下容易被主卡片遮挡，且文字颜色与背景融合，可读性极差。
3.  **卡片与背景不协调：** 无论是默认主题还是未来切换的主题，信息卡片 (`user-info-card`, `workspace-sections .card`) 都保持统一的“纸张色”，与主题背景图的色调、氛围往往不符，导致画面割裂。
4.  **缺乏沉浸感：** 整个页面感觉像是在一张静态背景图上堆叠了几个方块（卡片），而不是一个有深度、有生命力的“洞府场景”。
5.  **空间利用率：** 布局相对松散，信息排布不够紧凑，也没有利用主题背景的元素进行UI的融合。

**设计原则：**
1.  **分层与景深：** 将页面划分为清晰的层次：最远是“洞府场景背景”；中间是“工作台/核心信息区”；最近是“交互元素”。
2.  **视觉和谐：** 确保主题背景、核心信息卡片、文字颜色之间有足够的对比度，同时保持整体色调和风格的统一。
3.  **像素艺术的“物理感”：** 利用像素画的块状特性，通过清晰的描边、阴影来模拟按钮、卡片、卷轴的“物理”存在感。
4.  **叙事性UI：** 让UI元素本身也成为“江湖故事”的一部分，例如卡片像卷轴，按钮像印章。
5.  **信息聚焦：** 确保核心信息（用户资料、打卡状态）突出，且易于阅读和操作。

### 2. 优化方案与具体实施

**核心思路：**
*   **将主题背景应用到全局 `body` 或 `#app` 容器，作为整个应用甚至页面的“天空”或“场景”。**
*   **`Workspace.vue` 中的所有内容卡片和信息展示，作为这个场景上的“UI元素”或“家具”，拥有自己的明确背景（如半透明的宣纸或木质面板）。**
*   **重新设计页面标题，使其无论在何种背景下都清晰可见。**

#### **2.1. 前端 `frontend/src/main.js` (修改文件)**

将主题类应用到 `body` 元素，确保主题背景覆盖整个视口，不被 Vue 应用容器遮挡。

```javascript
// File path: frontend/src/main.js
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import './styles/ancient-pixel.scss'
import './style.css'

const app = createApp(App)
const pinia = createPinia(); // 创建 pinia 实例

app.use(pinia) // 先使用 pinia
app.use(router)

// **新增：监听用户主题偏好并应用到 body**
// 在应用挂载前导入 userStore，确保 setup() 外部可用
import { useUserStore } from './stores/user';
router.isReady().then(() => { // 确保路由准备就绪，可以安全访问 store
  const userStore = useUserStore();
  // 初次加载时尝试获取用户信息，以便设置主题
  if (!userStore.userInfo && userStore.token) {
    userStore.fetchUserInfo().then(() => {
      // 获取到用户信息后设置主题
      const theme = userStore.userInfo?.preferences?.theme || 'default';
      document.body.classList.add(`theme-${theme}-background`);
    });
  } else {
    // 如果已有用户信息或没有token，直接设置主题
    const theme = userStore.userInfo?.preferences?.theme || 'default';
    document.body.classList.add(`theme-${theme}-background`);
  }

  // 监听主题变化并动态更新 body class
  userStore.$subscribe((mutation, state) => {
    if (mutation.payload?.userInfo?.preferences?.theme !== undefined) {
      const oldTheme = document.body.className.match(/theme-(\w+)-background/);
      if (oldTheme) {
        document.body.classList.remove(oldTheme[0]);
      }
      document.body.classList.add(`theme-${state.userInfo.preferences.theme}-background`);
    }
  });

  app.mount('#app');
});
```

#### **2.2. 前端 `frontend/src/styles/ancient-pixel.scss` (修改文件)**

定义全局主题背景样式和卡片透明度。

```scss
// File path: frontend/src/styles/ancient-pixel.scss
// ... (现有颜色变量和通用样式保持不变)

// **新增：全局主题背景样式**
body {
  font-family: 'ZCOOL KuaiLe', 'Noto Serif SC', serif, 'Pixelify Sans', monospace, sans-serif;
  background-color: var(--color-ancient-paper); // 默认背景色，会被主题图覆盖
  color: var(--color-ancient-dark-brown);
  background-repeat: no-repeat; // 默认不重复
  background-position: center center; // 默认居中
  background-size: cover; // 默认覆盖
  transition: background-image 0.5s ease-in-out; // 背景切换动画
  image-rendering: pixelated; // 确保像素化
}

// 主题背景类 (应用到 body)
.theme-default-background {
  background-image: url('/backgrounds/bg_default.png');
  background-size: 120px 120px; // 小方块平铺，不覆盖
  background-repeat: repeat; // 重复
}

.theme-forest-background {
  background-image: url('/backgrounds/bg_forest.png');
  background-size: cover;
}

.theme-cave-background {
  background-image: url('/backgrounds/bg_cave.png');
  background-size: cover;
}

.theme-ancient-hall-background {
  background-image: url('/backgrounds/bg_ancient_hall.png');
  background-size: cover;
}

// 古风像素容器样式
.ancient-pixel-container {
  background-color: var(--color-ancient-paper); // **保持纸张色背景**
  border: 3px solid var(--color-ancient-dark-brown);
  border-radius: 0;
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);
  padding: 25px;
  // margin: 20px; // 移除，由父组件控制布局
  position: relative;
  // **新增：卡片半透明效果，让背景透出来**
  background-color: rgba(var(--rgb-ancient-paper), 0.9); // 使用 RGBA 颜色和变量
  backdrop-filter: blur(2px); // 模糊背景，增强层次感（可选，像素风可能不适合）
  // 确保颜色变量有 RGB 值
  // :root { --rgb-ancient-paper: 248, 231, 213; }
}

// **在 :root 增加 rgb 颜色变量**
:root {
  /* Design System Colors - New for Pixel/Ancient Martial Arts Style */
  --color-ancient-paper: #F8E7D5; /* 古朴的纸张色 */
  --rgb-ancient-paper: 248, 231, 213; // **新增 RGB 值**
  --color-ancient-dark-brown: #5A3F2B; /* 深棕色，用于主要文字和边框 */
  --rgb-ancient-dark-brown: 90, 63, 43; // **新增 RGB 值**
  // ... 其他颜色变量保持不变 ...
}

// ... 其他通用样式保持不变 ...
```

#### **2.3. 前端 `frontend/src/views/Workspace.vue` (修改文件)**

核心修改：
*   移除 `.workspace-page` 上的 `ancient-pixel-container` 类和主题类绑定，让它专注于作为页面的根容器。
*   引入一个新的 `div` 作为内容的实际容器，并将其设置为 `.ancient-pixel-container`。
*   调整页面标题的样式和位置，使其独立于内容卡片。

```vue
<script setup>
// File path: frontend/src/views/Workspace.vue
import { ref, onMounted, computed, watch } from 'vue';
import axios from 'axios';
import { useUserStore } from '@/stores/user';

const userStore = useUserStore();

const userData = computed(() => userStore.userInfo);
const loading = computed(() => userStore.loading);
const error = computed(() => userStore.error);
const fullAvatarUrl = computed(() => userStore.fullAvatarUrl);

const selectedFile = ref(null);
const uploading = ref(false);
const uploadSuccess = ref(false);
const uploadError = ref(null);

const editingProfile = ref(false);
const editableNickname = ref('');
const editablePosition = ref('');
const editableSignature = ref('');
const profileUpdateError = ref(null);
const profileUpdateSuccess = ref(false);

const availableThemes = ref([
  { id: 'default', name: '云雾缭绕 (基础)', icon: '☁️' }, // 更新名称
  { id: 'forest', name: '翠竹幽林 (静谧)', icon: '🌳' },
  { id: 'cave', name: '石窟洞府 (隐蔽)', icon: '🪨' },
  { id: 'ancient-hall', name: '殿宇楼阁 (宏伟)', icon: '🏛️' }
]);
const selectedThemeId = ref('default');

// 监听 userData 变化，初始化编辑字段和主题
watch(userData, (newVal) => {
  if (newVal) {
    editableNickname.value = newVal.nickname || '';
    editablePosition.value = newVal.position || '';
    editableSignature.value = newVal.signature || '';
    selectedThemeId.value = newVal.preferences?.theme || 'default';
  }
}, { immediate: true });

// **移除 currentThemeClass computed property，因为类现在直接加到 body 上**


const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    selectedFile.value = file;
    uploadSuccess.value = false;
    uploadError.value = null;
  }
};

const triggerFileInput = () => {
  const fileInput = document.getElementById('avatarFile');
  if (fileInput) {
    fileInput.click();
  }
};

const uploadAvatar = async () => { /* ... (保持不变) ... */
  uploading.value = true;
  uploadError.value = null;
  uploadSuccess.value = false;

  if (!selectedFile.value) {
    uploadError.value = '请选择一个文件';
    uploading.value = false;
    return;
  }

  const token = localStorage.getItem('token');
  if (!token) {
    uploadError.value = '侠士尚未入世，无法上传头像。';
    uploading.value = false;
    return;
  }

  const formData = new FormData();
  formData.append('avatar', selectedFile.value);

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/avatar`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.data && response.data.success) {
      uploadSuccess.value = true;
      if (response.data.avatarPath || response.data.avatarUrl) {
         userStore.updateAvatar(response.data.avatarPath || response.data.avatarUrl);
      }
      console.log('头像上传成功:', response.data);
    } else {
      uploadError.value = response.data.message || '头像上传失败';
    }
  } catch (err) {
    console.error('头像上传错误:', err);
    uploadError.value = err.response?.data?.message || '网络或服务器错误';
  } finally {
    uploading.value = false;
    selectedFile.value = null;
  }
};

const saveProfile = async () => {
  profileUpdateError.value = null;
  profileUpdateSuccess.value = false;

  const updates = {
    nickname: editableNickname.value,
    position: editablePosition.value,
    signature: editableSignature.value,
    preferences: {
      theme: selectedThemeId.value
    }
  };

  const result = await userStore.updateUserInfo(updates);
  if (result.success) {
    profileUpdateSuccess.value = true;
    editingProfile.value = false;
    setTimeout(() => {
      profileUpdateSuccess.value = false;
    }, 3000);
  } else {
    profileUpdateError.value = result.message;
  }
};

const handleThemeChange = (event) => {
  selectedThemeId.value = event.target.value;
  // 注意：此处不会实时更改 body 的 class，而是等到 saveProfile 成功后 userStore.$subscribe 才会触发
  // 这种延迟是为了确保用户确认保存后才改变 UI 状态
};


onMounted(() => {
  if (!userStore.userInfo && !userStore.loading) {
    userStore.fetchUserInfo();
  }
});

</script>

<template>
  <!-- **修改：移除 ancient-pixel-container 和 currentThemeClass** -->
  <div class="workspace-page">
    <!-- 独立的页面标题容器 -->
    <div class="page-title-container">
      <h1 class="page-title">我的工位 · 洞府</h1>
    </div>

    <!-- **新增：主内容容器，应用 ancient-pixel-container** -->
    <div class="workspace-content-wrapper ancient-pixel-container">
      <div v-if="loading" class="loading-state">
        洞府信息载入中，请稍候...
      </div>

      <div v-else-if="error" class="error-state">
        错误：{{ error }}
      </div>

      <div v-else-if="userData" class="user-info-card card">

        <div class="avatar">
          <input
            type="file"
            ref="avatarFileInput"
            id="avatarFile"
            @change="handleFileChange"
            accept="image/*"
            style="display: none;"
          />
          <img
            :src="userStore.fullAvatarUrl"
            alt="用户头像"
            class="user-avatar"
            @click="triggerFileInput"
          />
          <div v-if="selectedFile" class="upload-status-text">待上传: {{ selectedFile.name }}</div>
          <div v-if="uploading" class="upload-status-text">上传中...</div>
          <div v-if="uploadSuccess" class="upload-status-text success">上传成功!</div>
          <div v-if="uploadError" class="upload-status-text error">{{ uploadError }}</div>
          <button
            v-if="selectedFile && !uploading"
            @click.stop="uploadAvatar"
            class="pixel-button primary upload-button"
          >
            上传洞府画像
          </button>
        </div>

        <div class="details">
          <h2 class="user-main-name">{{ userData.username }}</h2>
          <p class="user-email">法号: {{ userData.email }}</p>
          <p class="user-join-date">入世时间: {{ new Date(userData.createdAt).toLocaleDateString() }}</p>

          <div v-if="editingProfile" class="edit-profile-form">
              <div class="form-group">
                  <label for="nickname">江湖名号:</label>
                  <input type="text" id="nickname" v-model="editableNickname" class="pixel-input" />
              </div>
              <div class="form-group">
                  <label for="position">当前位阶:</label>
                  <input type="text" id="position" v-model="editablePosition" class="pixel-input" />
              </div>
              <div class="form-group">
                  <label for="signature">江湖宣言:</label>
                  <textarea id="signature" v-model="editableSignature" rows="3" class="pixel-input"></textarea>
              </div>
              <div class="form-group">
                  <label for="theme">洞府主题:</label>
                  <select id="theme" v-model="selectedThemeId" @change="handleThemeChange" class="pixel-input">
                      <option v-for="theme in availableThemes" :key="theme.id" :value="theme.id">
                          {{ theme.icon }} {{ theme.name }}
                      </option>
                  </select>
              </div>
              <div v-if="profileUpdateError" class="error-message shaking-pixel">{{ profileUpdateError }}</div>
              <div v-if="profileUpdateSuccess" class="success-message">资料更新成功！</div>
              <div class="profile-actions">
                <button @click="saveProfile" class="pixel-button primary">保存资料</button>
                <button @click="editingProfile = false" class="pixel-button secondary cancel-button">取消</button>
              </div>
          </div>
          <div v-else class="display-profile">
              <p>江湖名号: <span>{{ userData.nickname || '未设置' }}</span></p>
              <p>当前位阶: <span>{{ userData.position || '未设置' }}</span></p>
              <p>江湖宣言: <span>{{ userData.signature || '未设置' }}</span></p>
              <p>洞府主题: <span>{{ availableThemes.find(t => t.id === selectedThemeId)?.name || '未设置' }}</span></p>
              <button @click="editingProfile = true" class="pixel-button primary edit-button">编辑资料</button>
          </div>
        </div>
      </div>

      <div v-else class="no-data-state">
        未能加载侠士洞府信息。
      </div>

      <div class="workspace-sections">
          <div class="card">
              <h3>近期江湖历练</h3>
              <p>暂无近期历练信息。</p>
              <p>（待开发）</p>
          </div>

           <div class="card">
              <h3>洞府秘藏</h3>
              <ul>
                  <li><a href="#">摸鱼功法研习录</a></li>
                  <li><a href="#">江湖声望排行碑</a></li>
              </ul>
          </div>
      </div>
    </div> <!-- end workspace-content-wrapper -->
  </div>
</template>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss';

.workspace-page {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  // **此处的背景设置已被 main.js 中的 body 样式取代，只保留padding/flex等布局属性**
  // background-color: var(--color-ancient-paper);
}

// **新增：页面标题容器样式**
.page-title-container {
  width: 100%;
  max-width: 600px; /* 与内容卡片宽度对齐 */
  background-color: var(--color-ancient-ink); /* 墨色背景 */
  border: 3px solid var(--color-ancient-dark-brown); /* 边框 */
  border-radius: 0;
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);
  padding: 15px 25px;
  margin-bottom: 20px;
  text-align: center;
  position: relative; /* 确保 z-index 有效 */
  z-index: 10; /* 确保标题在背景之上 */
}

.page-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 2em;
  color: var(--color-ancient-paper); /* 标题文字颜色与背景形成对比 */
  margin: 0; // 移除默认 margin
  text-align: center; // 标题居中
}

// **新增：主内容包裹器，现在它获得了 ancient-pixel-container 的样式**
.workspace-content-wrapper {
  // @extend .ancient-pixel-container; // 继承通用容器样式
  // 注意：在 template 中已直接添加 class="ancient-pixel-container"
  width: 100%;
  max-width: 600px; // 与标题宽度对齐
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px; // 内部卡片间距
  padding: 25px; // 继承了 ancient-pixel-container 的 padding

  // 内部的卡片应该有明确的背景色，以确保在主题背景下依然清晰可见
  .card {
    background-color: var(--color-ancient-paper); // 强制纸张色
    box-shadow: 6px 6px 0px var(--color-ancient-dark-brown); // 重新定义阴影，使其更突出
    border: 2px solid var(--color-ancient-dark-brown); // 明确边框
    width: 100%; // 确保内部卡片占据可用宽度
  }
}


.loading-state,
.error-state,
.no-data-state {
  // 这些状态卡片也应继承 .card 样式
  @extend .card;
  text-align: center;
  padding: 20px;
  color: var(--color-ancient-dark-brown);
  width: 100%;
}

.error-state {
    color: #c62828;
    background-color: #ffebee;
    border: 1px solid #ef9a9a;
    font-family: 'Pixelify Sans', monospace;
}

.user-info-card.card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  // 继承自 .workspace-content-wrapper .card，这里仅做布局调整
}

.avatar {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 0;
    border: 3px solid var(--color-ancient-dark-brown);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    cursor: pointer;
    background-color: var(--color-ancient-light-brown);
    flex-shrink: 0;
    image-rendering: pixelated;

    .user-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .upload-status-text {
        position: absolute;
        bottom: 5px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 11px;
        font-family: 'Pixelify Sans', monospace;
        color: var(--color-ancient-ink);
        background-color: rgba(var(--rgb-ancient-paper), 0.8); // 稍微透明
        padding: 2px 5px;
        border-radius: 0;
        z-index: 5;
    }

    .upload-status-text.success {
        color: var(--color-ancient-jade);
    }

    .upload-status-text.error {
        color: var(--color-ancient-blood-red);
    }

    .upload-button {
      margin-top: 5px;
      font-size: 0.8em;
      padding: 5px 10px;
      border-width: 1px;
      box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
      z-index: 5;
      &:hover {
        transform: translate(-1px, -1px);
        box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
      }
      &:active {
        transform: translate(1px, 1px);
        box-shadow: 1px 1px 0px var(--color-ancient-light-brown);
      }
    }
}

.details {
    flex-grow: 1;

    h2.user-main-name {
        font-family: 'ZCOOL KuaiLe', serif;
        font-size: 1.5em;
        color: var(--color-ancient-ink);
        margin: 0 0 5px 0;
    }

    p {
        font-family: 'Noto Serif SC', serif;
        font-size: 1em;
        color: var(--color-ancient-dark-brown);
        margin: 3px 0;
    }
}


/* Profile Edit Form */
.edit-profile-form {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed var(--color-ancient-light-brown);

  .form-group {
    margin-bottom: 10px;
    label {
      display: block;
      margin-bottom: 5px;
      font-family: 'Pixelify Sans', monospace;
      font-weight: bold;
      color: var(--color-ancient-dark-brown);
      font-size: 1.1em;
    }
    input[type="text"], textarea, select {
      @extend .pixel-input;
    }
  }
  .profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
  }
  .cancel-button {
    background-color: var(--color-ancient-stone-gray);
    border-color: var(--color-ancient-stone-gray-dark);
    color: var(--color-neutral-white);
    &:hover {
      background-color: var(--color-ancient-stone-gray-light);
    }
  }
  .error-message, .success-message {
      @extend .error-message;
      margin-top: 10px;
      margin-bottom: 20px;
  }
  .success-message {
      border-color: var(--color-ancient-jade);
      color: var(--color-ancient-jade-dark);
      background-color: var(--color-ancient-paper);
  }
}

/* Profile Display Area */
.display-profile {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed var(--color-ancient-light-brown);
  p {
    font-family: 'Noto Serif SC', serif;
    font-size: 0.95em;
    margin-bottom: 8px;
    color: var(--color-ancient-dark-brown);
    span {
      font-weight: bold;
      color: var(--color-ancient-ink);
    }
  }
  .edit-button {
    margin-top: 10px;
  }
}


.workspace-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    width: 100%;
    // 这些卡片现在也继承了 .workspace-content-wrapper .card 的样式
}

.workspace-sections .card h3 {
    font-family: 'ZCOOL KuaiLe', serif;
    font-size: 1.3em;
    color: var(--color-ancient-ink);
    margin: 0 0 10px 0;
    border-bottom: 1px dashed var(--color-ancient-light-brown);
    padding-bottom: 5px;
}

.workspace-sections .card p,
.workspace-sections .card ul {
     font-family: 'Noto Serif SC', serif;
     font-size: 0.9em;
     color: var(--color-ancient-dark-brown);
     margin: 0;
     padding: 0;
}

.workspace-sections .card ul {
    list-style: none;
}

.workspace-sections .card ul li {
    margin-bottom: 5px;
}

.workspace-sections .card ul li a {
     color: var(--color-ancient-jade);
     text-decoration: none;

     &:hover {
         text-decoration: underline;
     }
}

// Responsive adjustments
@media (max-width: 600px) {
  .user-info-card.card {
    flex-direction: column;
    text-align: center;
  }

  .avatar {
      margin-bottom: 10px;
  }

  .details {
      text-align: center;
  }

   .workspace-sections {
       grid-template-columns: 1fr;
   }
}
</style>
```

#### **2.4. 前端 `frontend/src/styles/style.css` (修改文件)**

在 `:root` 中添加 RGB 颜色变量，供 SCSS 文件中的 `rgba()` 使用。

```css
/* File path: frontend/src/style.css */
:root {
  /* Design System Colors - New for Pixel/Ancient Martial Arts Style */
  --color-ancient-paper: #F8E7D5; /* 古朴的纸张色 */
  --rgb-ancient-paper: 248, 231, 213; /* **新增 RGB 值** */
  --color-ancient-dark-brown: #5A3F2B; /* 深棕色，用于主要文字和边框 */
  --rgb-ancient-dark-brown: 90, 63, 43; /* **新增 RGB 值** */
  // ... 其他颜色变量保持不变 ...
}

/* ... 其他全局样式保持不变 ... */
```

#### **2.5. 前端 `frontend/public/backgrounds/` (图片更新)**

使用您提供的更美观的背景图。请将这些图片分别保存为：

*   `frontend/public/backgrounds/bg_default.png` (图3 - 窗外山水画卷)
*   `frontend/public/backgrounds/bg_forest.png` (图4 - 竹林亭台)
*   `frontend/public/backgrounds/bg_cave.png` (图2 - 洞穴瀑布)
*   `frontend/public/backgrounds/bg_ancient_hall.png` (图1 - 殿宇楼阁)

**重要：** 确保这些图片路径与 `ancient-pixel.scss` 中 `url()` 函数引用的路径一致。

---

### 3. 测试与验证步骤：

1.  **准备主题图片：** 确保您已将新的背景图片放入 `frontend/public/backgrounds/` 目录。
2.  **启动后端：**
    *   进入 `backend` 目录。
    *   运行 `npm install`。
    *   运行 `npm run dev` 启动后端服务器。
3.  **启动前端：**
    *   进入 `frontend` 目录。
    *   运行 `npm install`。
    *   运行 `npm run dev` 启动前端应用。
4.  **登录应用：**
    *   在浏览器中访问 `http://localhost:5173` 并登录。
5.  **测试洞府主题：**
    *   导航到“我的工位” (`/workspace`) 页面。
    *   **观察标题：** 确认页面顶部的“我的工位 · 洞府”标题不再被遮挡，并且在深色背景上以浅色文字清晰显示。
    *   **观察背景与卡片：** 确认页面背景现在是全屏的主题图片，并且核心卡片（用户资料卡、近期历练、洞府秘藏）以其“纸张色”的半透明效果清晰地浮动在背景之上，不再感觉冲突。
    *   **切换主题：**
        *   点击“编辑资料”按钮。
        *   在编辑表单中选择不同的“洞府主题”。
        *   点击“保存资料”按钮。
        *   观察整个页面背景是否平滑切换到新选择的主题。
        *   刷新页面，确认主题选择仍然保留。
    *   **表单和按钮：** 确认表单元素（输入框、选择框）和按钮的样式保持像素风格，且与整体主题协调。

通过这些优化，您的“我的工位”页面将具有更强的沉浸感和视觉吸引力，更好地符合像素武侠主题，让用户感受到真正置身于自己的“洞府”之中！