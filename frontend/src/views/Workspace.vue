<script setup>
import { ref, onMounted, computed, watch } from 'vue'; // 引入 watch
import axios from 'axios';
import { useUserStore } from '@/stores/user';

// 使用用户 Store
const userStore = useUserStore();

// 从 Store 中获取状态和计算属性
const userData = computed(() => userStore.userInfo); // 将 store 的 userInfo 映射到本地 userData
const loading = computed(() => userStore.loading);
const error = computed(() => userStore.error);
// fullAvatarUrl 直接从 Store 的 getter 中使用即可

const selectedFile = ref(null); // 用于存储用户选择的文件
const uploading = ref(false); // 上传状态
const uploadSuccess = ref(false); // 上传成功状态
const uploadError = ref(null); // 上传错误信息

// **新增：用于编辑的响应式数据**
const editingProfile = ref(false); // 是否处于编辑模式
const editableNickname = ref('');
const editablePosition = ref('');
const editableSignature = ref('');
const profileUpdateError = ref(null);    // 资料更新错误信息
const profileUpdateSuccess = ref(false); // 资料更新成功状态

// **新增：洞府主题相关**
const availableThemes = ref([
  { id: 'default', name: '居家府邸', icon: '☁️' },
  { id: 'forest', name: '翠竹幽林', icon: '🌳' },
  { id: 'cave', name: '石窟洞府', icon: '🪨' },
  { id: 'ancient-hall', name: '殿宇楼阁', icon: '🏛️' }
]);
const selectedThemeId = ref('default'); // 当前选择的主题ID

// 监听 userData 变化，初始化编辑字段和主题
watch(userData, (newVal) => {
  if (newVal) {
    editableNickname.value = newVal.nickname || '';
    editablePosition.value = newVal.position || '';
    editableSignature.value = newVal.signature || '';
    // **新增：初始化主题**
    selectedThemeId.value = newVal.preferences?.theme || 'default';
  }
}, { immediate: true });

// Computed property to apply theme class
const currentThemeClass = computed(() => {
  return `theme-${selectedThemeId.value}`;
});

// 处理文件选择
const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    selectedFile.value = file;
    uploadSuccess.value = false; // 重置成功状态
    uploadError.value = null; // 重置错误状态
    // 可以在这里预览图片，如果需要的话
    // const reader = new FileReader();
    // reader.onload = (e) => { userPreviewAvatar.value = e.target.result; };
    // reader.readAsDataURL(file);
  }
};

// 触发文件输入点击
const triggerFileInput = () => {
  const fileInput = document.getElementById('avatarFile');
  if (fileInput) {
    fileInput.click();
  }
};

// 上传头像函数
const uploadAvatar = async () => {
  uploading.value = true;
  uploadError.value = null;
  uploadSuccess.value = false;

  if (!selectedFile.value) {
    uploadError.value = '请选择一个文件';
    uploading.value = false;
    return;
  }

  const token = localStorage.getItem('token');
  if (!token) {
    uploadError.value = '侠士尚未入世，无法上传头像。';
    uploading.value = false;
    return;
  }

  const formData = new FormData();
  // 'avatar' 需要与后端 multer 配置中的 field name 匹配
  formData.append('avatar', selectedFile.value);

  // 从 Store 获取 API_BASE_URL
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'; // 也可以直接在 Store 中调用 API，这里为了简单直接获取环境变量

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/avatar`, formData, { // 使用 API_BASE_URL
      headers: {
        'Authorization': `Bearer ${token}`,
        // 'Content-Type': 'multipart/form-data' // axios 会自动设置 Content-Type
      },
    });

    if (response.data && response.data.success) {
      uploadSuccess.value = true;
      // 上传成功后更新 Store 中的用户头像信息
      if (response.data.avatarPath || response.data.avatarUrl) {
         userStore.updateAvatar(response.data.avatarPath || response.data.avatarUrl); // 调用 Store 的 action 更新头像路径
      }
      console.log('头像上传成功:', response.data);
    } else {
      uploadError.value = response.data.message || '头像上传失败';
    }
  } catch (err) {
    console.error('头像上传错误:', err);
    uploadError.value = err.response?.data?.message || '网络或服务器错误';
  } finally {
    uploading.value = false;
    selectedFile.value = null; // 上传完成后清空选中文件状态
  }
};

// 保存个人资料，包括主题
const saveProfile = async () => {
  profileUpdateError.value = null;
  profileUpdateSuccess.value = false;

  const updates = {
    nickname: editableNickname.value,
    position: editablePosition.value,
    signature: editableSignature.value,
    // **新增：将主题添加到更新对象中**
    preferences: {
      theme: selectedThemeId.value
    }
  };

  const result = await userStore.updateUserInfo(updates);
  if (result.success) {
    profileUpdateSuccess.value = true;
    editingProfile.value = false;
    setTimeout(() => {
      profileUpdateSuccess.value = false;
    }, 3000);
  } else {
    profileUpdateError.value = result.message;
  }
};

// **新增：处理主题选择变化**
const handleThemeChange = (event) => {
  selectedThemeId.value = event.target.value;
  // 实时保存主题，或者在点击"保存资料"时统一保存
  // 考虑到用户体验，实时更新会更好，但这里为了简化，先统一在保存资料时更新。
  // 如果需要实时更新，可以单独抽离一个 saveTheme 函数，只调用 updateUserInfo({ preferences: { theme: newTheme } })
};

// 在组件挂载时获取用户数据 (如果 Store 中还没有)
onMounted(() => {
  if (!userStore.userInfo && !userStore.loading) { // 避免重复加载
    userStore.fetchUserInfo();
  }
});

</script>

<template>
  <div class="workspace-page ancient-pixel-container" :class="currentThemeClass">
    <h1 class="page-title">我的工位 · 洞府</h1>

    <div v-if="loading" class="loading-state">
      洞府信息载入中，请稍候...
    </div>

    <div v-else-if="error" class="error-state">
      错误：{{ error }}
    </div>

    <div v-else-if="userData" class="user-info-card card">
      <div class="avatar">
        <!-- 隐藏的文件输入框 -->
        <input
          type="file"
          ref="avatarFileInput"
          id="avatarFile"
          @change="handleFileChange"
          accept="image/*"
          style="display: none;"
        />
        <!-- 头像图片，点击可触发文件选择 -->
        <img
          :src="userStore.fullAvatarUrl"
          alt="用户头像"
          class="user-avatar"
          @click="triggerFileInput"
        />
        <!-- 头像上传状态和错误提示 -->
        <div v-if="selectedFile" class="upload-status-text">待上传: {{ selectedFile.name }}</div>
        <div v-if="uploading" class="upload-status-text">上传中...</div>
        <div v-if="uploadSuccess" class="upload-status-text success">上传成功!</div>
        <div v-if="uploadError" class="upload-status-text error">{{ uploadError }}</div>
        <!-- 上传头像按钮，仅当选择了文件且不在上传中时显示 -->
        <button
           v-if="selectedFile && !uploading"
           @click.stop="uploadAvatar"
           class="pixel-button primary upload-button"
        >
          上传洞府画像
        </button>
      </div>

      <div class="details">
        <h2 class="user-main-name">{{ userData.username }}</h2>
        <p class="user-email">法号: {{ userData.email }}</p>
        <p class="user-join-date">入世时间: {{ new Date(userData.createdAt).toLocaleDateString() }}</p>

        <!-- 资料编辑区 -->
        <div v-if="editingProfile" class="edit-profile-form">
            <div class="form-group">
                <label for="nickname">江湖名号:</label>
                <input type="text" id="nickname" v-model="editableNickname" class="pixel-input" />
            </div>
            <div class="form-group">
                <label for="position">当前位阶:</label>
                <input type="text" id="position" v-model="editablePosition" class="pixel-input" />
            </div>
            <div class="form-group">
                <label for="signature">江湖宣言:</label>
                <textarea id="signature" v-model="editableSignature" rows="3" class="pixel-input"></textarea>
            </div>
            <!-- **新增：洞府主题选择** -->
            <div class="form-group">
                <label for="theme">洞府主题:</label>
                <select id="theme" v-model="selectedThemeId" @change="handleThemeChange" class="pixel-input">
                    <option v-for="theme in availableThemes" :key="theme.id" :value="theme.id">
                        {{ theme.icon }} {{ theme.name }}
                    </option>
                </select>
            </div>
            <!-- 资料更新错误和成功提示 -->
            <div v-if="profileUpdateError" class="error-message">{{ profileUpdateError }}</div>
            <div v-if="profileUpdateSuccess" class="success-message">资料更新成功！</div>
            <div class="profile-actions">
              <button @click="saveProfile" class="pixel-button primary">保存资料</button>
              <button @click="editingProfile = false" class="pixel-button secondary cancel-button">取消</button>
            </div>
        </div>
        <!-- 资料显示区 -->
        <div v-else class="display-profile">
            <p>江湖名号: <span>{{ userData.nickname || '未设置' }}</span></p>
            <p>当前位阶: <span>{{ userData.position || '未设置' }}</span></p>
            <p>江湖宣言: <span>{{ userData.signature || '未设置' }}</span></p>
            <!-- **新增：显示当前洞府主题** -->
            <p>洞府主题: <span>{{ availableThemes.find(t => t.id === selectedThemeId)?.name || '未设置' }}</span></p>
            <button @click="editingProfile = true" class="pixel-button primary edit-button">编辑资料</button>
        </div>
      </div>
    </div>

    <div v-else class="no-data-state">
      未能加载侠士洞府信息。
    </div>


    <div class="workspace-sections">

        <div class="card">
            <h3>近期江湖历练</h3>
            <p>暂无近期历练信息。</p>
            <p>（待开发）</p>
        </div>

         <div class="card">
            <h3>洞府秘藏</h3>
            <ul>
                <li><a href="#">摸鱼功法研习录</a></li>
                <li><a href="#">江湖声望排行碑</a></li>
            </ul>
        </div>
    </div>

  </div>
</template>

<style scoped lang="scss">
// **重要：确保这里是 lang="scss" 以支持 @extend**
@use '../styles/ancient-pixel.scss'; // 引入新的 SCSS 文件

.workspace-page {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  // **基础背景色，会被主题背景图覆盖**
  background-color: var(--color-ancient-paper);
  background-repeat: repeat; // Default repeat for tiling patterns
  background-position: center center;
  background-size: auto; // Default size

  // **新增：洞府主题样式**
  &.theme-default {
    background-image: url('/backgrounds/bg_default.png'); // 假定图片在     
    background-size: cover; // 覆盖整个区域
    background-position: bottom center;
    image-rendering: pixelated;
  }
  &.theme-forest {
    background-image: url('/backgrounds/bg_forest.png');
    background-size: cover; // 覆盖整个区域
    background-position: bottom center;
    image-rendering: pixelated;
  }
  &.theme-cave {
    background-image: url('/backgrounds/bg_cave.png');
    background-size: cover;
    background-position: center center;
    image-rendering: pixelated;
  }
  &.theme-ancient-hall {
    background-image: url('/backgrounds/bg_ancient_hall.png');
    background-size: cover;
    background-position: center center;
    image-rendering: pixelated;
  }
}

.page-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 2em;
  color: var(--color-ancient-ink);
  margin-bottom: 10px;
  text-align: left; // Typically page titles are left-aligned
}

.loading-state,
.error-state,
.no-data-state {
  text-align: center;
  padding: 20px;
  background-color: var(--color-ancient-paper);
  border: 2px solid var(--color-ancient-dark-brown);
  border-radius: 0; // Pixel style
  box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  color: var(--color-ancient-dark-brown);
}

.error-state {
    color: #c62828;
    background-color: #ffebee;
    border: 1px solid #ef9a9a;
    font-family: 'Pixelify Sans', monospace;
}

.user-info-card.card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background-color: var(--color-ancient-paper);
  border-radius: 0;
  border: 3px solid var(--color-ancient-dark-brown);
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);
  width: 100%;
  max-width: 600px;
}

.avatar {
    position: relative; /* 为了定位上传状态文本 */
    width: 100px;
    height: 100px;
    border-radius: 0; // Square pixel style
    border: 3px solid var(--color-ancient-dark-brown);
    display: flex;
    flex-direction: column; // Allow vertical stacking for button and text
    align-items: center;
    justify-content: center;
    overflow: hidden;
    cursor: pointer; /* 表示可点击 */
    background-color: var(--color-ancient-light-brown); /* 默认背景色 */
    flex-shrink: 0; /* 防止头像区域被压缩 */
    image-rendering: pixelated; // Ensure pixelation for images

    .user-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover; /* 确保图片覆盖整个区域 */
    }

    .upload-status-text {
        position: absolute;
        bottom: 5px; // Adjusted for button
        left: 0;
        right: 0;
        text-align: center;
        font-size: 11px; // Smaller font for status
        font-family: 'Pixelify Sans', monospace;
        color: var(--color-ancient-ink);
        background-color: rgba(255, 255, 255, 0.7); /* 半透明背景 */
        padding: 2px 5px;
        border-radius: 0; // Pixel
        z-index: 5; // Make sure text is on top
    }

    .upload-status-text.success {
        color: var(--color-ancient-jade);
    }

    .upload-status-text.error {
        color: var(--color-ancient-blood-red);
    }

    .upload-button {
      margin-top: 5px; /* Spacing between status text and button */
      font-size: 0.8em;
      padding: 5px 10px;
      border-width: 1px;
      box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
      z-index: 5; // Make sure button is on top
      &:hover {
        transform: translate(-1px, -1px);
        box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
      }
      &:active {
        transform: translate(1px, 1px);
        box-shadow: 1px 1px 0px var(--color-ancient-light-brown);
      }
    }
}

.details {
    flex-grow: 1;

    h2.user-main-name {
        font-family: 'ZCOOL KuaiLe', serif;
        font-size: 1.5em;
        color: var(--color-ancient-ink);
        margin: 0 0 5px 0;
    }

    p {
        font-family: 'Noto Serif SC', serif;
        font-size: 1em;
        color: var(--color-ancient-dark-brown);
        margin: 3px 0;
    }
}


/* Profile Edit Form */
.edit-profile-form {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed var(--color-ancient-light-brown);

  .form-group {
    margin-bottom: 10px;
    label {
      display: block;
      margin-bottom: 5px;
      font-family: 'Pixelify Sans', monospace;
      font-weight: bold;
      color: var(--color-ancient-dark-brown);
    }
    input[type="text"], textarea, select {
      // **直接定义像素风格输入框样式**
      width: 100%;
      padding: 8px 12px;
      border: 2px solid var(--color-ancient-dark-brown);
      border-radius: 0; // 锐利边缘
      background-color: var(--color-ancient-paper);
      color: var(--color-ancient-ink);
      font-family: 'Pixelify Sans', monospace;
      font-size: 0.9em;
      box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--color-ancient-jade);
        box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
        transform: translate(-1px, -1px);
      }

      &::placeholder {
        color: var(--color-ancient-stone-gray);
        font-style: italic;
      }
    }
  }
  .profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
  }
  .cancel-button {
    background-color: var(--color-ancient-stone-gray);
    border-color: var(--color-ancient-stone-gray-dark);
    color: var(--color-neutral-white);
    &:hover {
      background-color: var(--color-ancient-stone-gray-light);
    }
  }
  .error-message, .success-message {
      text-align: center;
      padding: 8px;
      border-radius: 0;
      font-size: 0.9em;
      font-family: 'Pixelify Sans', monospace;
      margin-bottom: 10px;
      border-width: 2px; // Make border visible
      border-style: solid;
  }
  .error-message {
      color: var(--color-ancient-blood-red);
      background-color: var(--color-ancient-paper);
      border-color: var(--color-ancient-blood-red);
  }
  .success-message {
      color: var(--color-ancient-jade-dark);
      background-color: var(--color-ancient-paper);
      border-color: var(--color-ancient-jade);
  }
}

/* Profile Display Area */
.display-profile {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed var(--color-ancient-light-brown);
  p {
    font-family: 'Noto Serif SC', serif;
    font-size: 0.95em;
    margin-bottom: 8px;
    color: var(--color-ancient-dark-brown);
    span {
      font-weight: bold;
      color: var(--color-ancient-ink);
    }
  }
  .edit-button {
    margin-top: 10px;
  }
}

.workspace-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    width: 100%;
    max-width: 600px;
}

.workspace-sections .card {
    padding: 20px;
    background-color: var(--color-ancient-paper);
    border-radius: 0;
    border: 2px solid var(--color-ancient-dark-brown);
    box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
}

.workspace-sections .card h3 {
    font-family: 'ZCOOL KuaiLe', serif;
    font-size: 1.3em;
    color: var(--color-ancient-ink);
    margin: 0 0 10px 0;
    border-bottom: 1px dashed var(--color-ancient-light-brown);
    padding-bottom: 5px;
}

.workspace-sections .card p,
.workspace-sections .card ul {
     font-family: 'Noto Serif SC', serif;
     font-size: 0.9em;
     color: var(--color-ancient-dark-brown);
     margin: 0;
     padding: 0;
}

.workspace-sections .card ul {
    list-style: none;
}

.workspace-sections .card ul li {
    margin-bottom: 5px;
}

.workspace-sections .card ul li a {
     color: var(--color-ancient-jade);
     text-decoration: none;

     &:hover {
         text-decoration: underline;
     }
}

// Responsive adjustments
@media (max-width: 600px) {
  .user-info-card.card {
    flex-direction: column;
    text-align: center;
  }

  .avatar {
      margin-bottom: 10px;
  }

  .details {
      text-align: center;
  }

   .workspace-sections {
       grid-template-columns: 1fr; /* Stack items on small screens */
   }
}
</style>