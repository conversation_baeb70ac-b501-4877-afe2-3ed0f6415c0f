import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import './styles/ancient-pixel.scss'
import './style.css'

const app = createApp(App)
const pinia = createPinia(); // 创建 pinia 实例

app.use(pinia) // 先使用 pinia
app.use(router)

// **新增：监听用户主题偏好并应用到 body**
// 在应用挂载前导入 userStore，确保 setup() 外部可用
import { useUserStore } from './stores/user';
router.isReady().then(() => { // 确保路由准备就绪，可以安全访问 store
  const userStore = useUserStore();
  // 初次加载时尝试获取用户信息，以便设置主题
  if (!userStore.userInfo && userStore.token) {
    userStore.fetchUserInfo().then(() => {
      // 获取到用户信息后设置主题
      const theme = userStore.userInfo?.preferences?.theme || 'default';
      document.body.classList.add(`theme-${theme}-background`);
    });
  } else {
    // 如果已有用户信息或没有token，直接设置主题
    const theme = userStore.userInfo?.preferences?.theme || 'default';
    document.body.classList.add(`theme-${theme}-background`);
  }

  // 监听主题变化并动态更新 body class
  userStore.$subscribe((mutation, state) => {
    if (mutation.payload?.userInfo?.preferences?.theme !== undefined) {
      const oldTheme = document.body.className.match(/theme-(\w+)-background/);
      if (oldTheme) {
        document.body.classList.remove(oldTheme[0]);
      }
      document.body.classList.add(`theme-${state.userInfo.preferences.theme}-background`);
    }
  });

  app.mount('#app');
});